using GCP.Common;
using GCP.DataAccess;
using GCP.Functions.Common.Models;
using GCP.FunctionPool.Flow;
using GCP.FunctionPool.Flow.Models;
using GCP.FunctionPool;
using System.Text.Json;
using LinqToDB;

namespace GCP.Functions.Common.Services
{
    [Function("actionLibrary", "动作库服务")]
    internal class ActionLibraryService : BaseService
    {
        [Function("getAll", "获取动作库清单")]
        public PagingData<LcActionLibrary> GetAll(string keyword = null, string category = null, string status = null, 
            int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();
            var query = from a in db.LcActionLibraries
                        where a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId &&
                        (string.IsNullOrEmpty(keyword) || a.Name.Contains(keyword) || 
                         (!string.IsNullOrEmpty(a.Description) && a.Description.Contains(keyword))) &&
                        (string.IsNullOrEmpty(category) || a.Category == category) &&
                        (string.IsNullOrEmpty(status) || a.Status == status)
                        select a;

            var total = query.Count();
            var data = query.Skip((pageIndex - 1) * pageSize)
                           .Take(pageSize)
                           .ToList();

            return new PagingData<LcActionLibrary>
            {
                List = data,
                Paging = new PagingInfo(total, pageSize, pageIndex)
            };
        }

        [Function("get", "获取动作库详情")]
        public LcActionLibrary Get(string id)
        {
            using var db = this.GetDb();
            var data = (from a in db.LcActionLibraries
                        where a.Id == id &&
                        a.State == 1 &&
                        a.SolutionId == this.SolutionId &&
                        a.ProjectId == this.ProjectId
                        select a).FirstOrDefault();
            return data;
        }

        [Function("save", "保存动作库")]
        public string Save(LcActionLibrary actionLibrary)
        {
            using var db = this.GetDb();
            
            if (string.IsNullOrEmpty(actionLibrary.Id))
            {
                // 新增
                actionLibrary.Id = TUID.NewTUID().ToString();
                actionLibrary.SolutionId = this.SolutionId;
                actionLibrary.ProjectId = this.ProjectId;
                actionLibrary.Creator = this.Context.clientID;
                actionLibrary.TimeCreate = DateTime.Now;
                actionLibrary.State = 1;
                actionLibrary.Version = 1;
                actionLibrary.ExecutionCount = 0;

                db.Insert(actionLibrary);
            }
            else
            {
                // 更新
                var existing = Get(actionLibrary.Id);
                if (existing == null)
                    throw new CustomException("动作库不存在");

                existing.Name = actionLibrary.Name;
                existing.Description = actionLibrary.Description;
                existing.Category = actionLibrary.Category;
                existing.Tags = actionLibrary.Tags;
                existing.Status = actionLibrary.Status;
                existing.FunctionFlowId = actionLibrary.FunctionFlowId;
                existing.FunctionFlowVersion = actionLibrary.FunctionFlowVersion;
                existing.InputSchemaJson = actionLibrary.InputSchemaJson;
                existing.OutputSchemaJson = actionLibrary.OutputSchemaJson;
                existing.TestDataJson = actionLibrary.TestDataJson;
                existing.Modifier = this.Context.clientID;
                existing.TimeModified = DateTime.Now;

                db.Update(existing);
            }

            return actionLibrary.Id;
        }

        [Function("delete", "删除动作库")]
        public void Delete(string id)
        {
            using var db = this.GetDb();
            var existing = Get(id);
            if (existing == null)
                throw new CustomException("动作库不存在");

            existing.State = 0;
            existing.Modifier = this.Context.clientID;
            existing.TimeModified = DateTime.Now;

            db.Update(existing);
        }

        [Function("getCategories", "获取动作库分类列表")]
        public List<string> GetCategories()
        {
            using var db = this.GetDb();
            var categories = (from a in db.LcActionLibraries
                             where a.State == 1 &&
                             a.SolutionId == this.SolutionId &&
                             a.ProjectId == this.ProjectId &&
                             !string.IsNullOrEmpty(a.Category)
                             select a.Category).Distinct().ToList();
            return categories;
        }

        [Function("copy", "复制动作库")]
        public string Copy(string id, string newName = null)
        {
            var original = Get(id);
            if (original == null)
                throw new CustomException("动作库不存在");

            var copy = new LcActionLibrary
            {
                Name = newName ?? $"{original.Name}_副本",
                Description = original.Description,
                Category = original.Category,
                Tags = original.Tags,
                Status = "draft", // 复制的动作库默认为草稿状态
                FunctionFlowId = original.FunctionFlowId,
                FunctionFlowVersion = original.FunctionFlowVersion,
                InputSchemaJson = original.InputSchemaJson,
                OutputSchemaJson = original.OutputSchemaJson,
                TestDataJson = original.TestDataJson
            };

            return Save(copy);
        }

        [Function("updateStatus", "更新动作库状态")]
        public void UpdateStatus(string id, string status)
        {
            using var db = this.GetDb();
            var existing = Get(id);
            if (existing == null)
                throw new CustomException("动作库不存在");

            existing.Status = status;
            existing.Modifier = this.Context.clientID;
            existing.TimeModified = DateTime.Now;

            db.Update(existing);
        }

        [Function("getExecutionStats", "获取动作库执行统计")]
        public object GetExecutionStats(string id, DateTime? startDate = null, DateTime? endDate = null)
        {
            using var db = this.GetDb();

            var actionLibrary = Get(id);
            if (actionLibrary == null)
                throw new CustomException("动作库不存在");

            var query = from log in db.LcActionLibraryLogs
                       where log.ActionLibraryId == id &&
                       log.SolutionId == this.SolutionId &&
                       log.ProjectId == this.ProjectId
                       select log;

            if (startDate.HasValue)
                query = query.Where(l => l.StartTime >= startDate.Value);
            if (endDate.HasValue)
                query = query.Where(l => l.StartTime <= endDate.Value);

            var logs = query.ToList();

            var totalExecutions = logs.Count;
            var successExecutions = logs.Count(l => l.Status == "success");
            var errorExecutions = logs.Count(l => l.Status == "error");
            var timeoutExecutions = logs.Count(l => l.Status == "timeout");

            var avgExecutionTime = logs.Where(l => l.ExecutionTimeMs.HasValue)
                                      .Select(l => l.ExecutionTimeMs.Value)
                                      .DefaultIfEmpty(0)
                                      .Average();

            return new
            {
                ActionLibraryId = id,
                ActionLibraryName = actionLibrary.Name,
                TotalExecutions = totalExecutions,
                SuccessExecutions = successExecutions,
                ErrorExecutions = errorExecutions,
                TimeoutExecutions = timeoutExecutions,
                SuccessRate = totalExecutions > 0 ? (double)successExecutions / totalExecutions * 100 : 0,
                AverageExecutionTime = Math.Round(avgExecutionTime, 2),
                LastExecutionTime = logs.OrderByDescending(l => l.StartTime).FirstOrDefault()?.StartTime
            };
        }

        [Function("testExecute", "测试执行动作库")]
        public async Task<object> TestExecute(string id, object inputData = null)
        {
            var actionLibrary = Get(id);
            if (actionLibrary == null)
                throw new CustomException("动作库不存在");

            var executionId = TUID.NewTUID().ToString();
            var startTime = DateTime.Now;

            try
            {
                // 获取函数流
                var functionService = new FunctionService();
                var function = functionService.Get(actionLibrary.FunctionFlowId);
                if (function == null)
                    throw new CustomException("关联的函数流不存在");

                // 获取函数流代码
                var functionCodeService = new FunctionCodeService();
                var version = actionLibrary.FunctionFlowVersion ?? function.UseVersion;
                var code = functionCodeService.GetCodeByVersion(actionLibrary.FunctionFlowId, version);
                if (string.IsNullOrEmpty(code))
                    throw new CustomException("函数流代码不存在");

                var flowInfo = JsonSerializer.Deserialize<FunctionFlow>(code);
                if (flowInfo == null)
                    throw new CustomException("函数流代码格式错误");

                // 准备执行上下文
                var context = new FunctionContext
                {
                    clientID = this.Context.clientID,
                    trackId = executionId,
                    globalData = new Dictionary<string, object>()
                };
                context.SolutionId = this.SolutionId;
                context.ProjectId = this.ProjectId;

                // 设置输入数据
                if (inputData != null)
                {
                    if (inputData is string jsonStr)
                    {
                        var parsedData = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonStr);
                        foreach (var kvp in parsedData)
                        {
                            context.globalData[kvp.Key] = kvp.Value;
                        }
                    }
                    else if (inputData is IDictionary<string, object> dictData)
                    {
                        foreach (var kvp in dictData)
                        {
                            context.globalData[kvp.Key] = kvp.Value;
                        }
                    }
                    else
                    {
                        context.globalData["input"] = inputData;
                    }
                }
                else if (!string.IsNullOrEmpty(actionLibrary.TestDataJson))
                {
                    // 使用测试数据
                    var testData = JsonSerializer.Deserialize<Dictionary<string, object>>(actionLibrary.TestDataJson);
                    foreach (var kvp in testData)
                    {
                        context.globalData[kvp.Key] = kvp.Value;
                    }
                }

                // 执行函数流
                var flowRunner = new FlowExecutor();
                flowRunner.Load(flowInfo);
                var result = await flowRunner.Run(context);

                var endTime = DateTime.Now;
                var executionTime = (int)(endTime - startTime).TotalMilliseconds;

                // 记录成功日志
                await LogExecution(actionLibrary.Id, executionId, inputData, result, "success",
                    executionTime, startTime, endTime);

                // 更新动作库统计信息
                await UpdateExecutionStats(actionLibrary.Id, executionTime);

                return new
                {
                    Success = true,
                    Result = result,
                    ExecutionTime = executionTime,
                    ExecutionId = executionId
                };
            }
            catch (Exception ex)
            {
                var endTime = DateTime.Now;
                var executionTime = (int)(endTime - startTime).TotalMilliseconds;

                // 记录错误日志
                await LogExecution(actionLibrary.Id, executionId, inputData, null, "error",
                    executionTime, startTime, endTime, ex.Message, ex.StackTrace);

                return new
                {
                    Success = false,
                    Error = ex.Message,
                    ExecutionTime = executionTime,
                    ExecutionId = executionId
                };
            }
        }

        [Function("getExecutionLogs", "获取动作库执行日志")]
        public PagingData<LcActionLibraryLog> GetExecutionLogs(string id, string status = null,
            DateTime? startDate = null, DateTime? endDate = null, int pageIndex = 1, int pageSize = 20)
        {
            using var db = this.GetDb();

            var query = from log in db.LcActionLibraryLogs
                       where log.ActionLibraryId == id &&
                       log.SolutionId == this.SolutionId &&
                       log.ProjectId == this.ProjectId
                       select log;

            if (!string.IsNullOrEmpty(status))
                query = query.Where(l => l.Status == status);
            if (startDate.HasValue)
                query = query.Where(l => l.StartTime >= startDate.Value);
            if (endDate.HasValue)
                query = query.Where(l => l.StartTime <= endDate.Value);

            var total = query.Count();
            var data = query.OrderByDescending(l => l.StartTime)
                           .Skip((pageIndex - 1) * pageSize)
                           .Take(pageSize)
                           .ToList();

            return new PagingData<LcActionLibraryLog>
            {
                List = data,
                Paging = new PagingInfo(total, pageSize, pageIndex)
            };
        }

        private async Task LogExecution(string actionLibraryId, string executionId, object inputData,
            object outputData, string status, int executionTime, DateTime startTime, DateTime endTime,
            string errorMessage = null, string stackTrace = null, string flowProcId = null, string flowStepId = null)
        {
            using var db = this.GetDb();

            var log = new LcActionLibraryLog
            {
                Id = TUID.NewTUID().ToString(),
                ActionLibraryId = actionLibraryId,
                ExecutionId = executionId,
                FlowProcId = flowProcId,
                FlowStepId = flowStepId,
                InputData = inputData != null ? JsonSerializer.Serialize(inputData) : null,
                OutputData = outputData != null ? JsonSerializer.Serialize(outputData) : null,
                ErrorMessage = errorMessage,
                StackTrace = stackTrace,
                Status = status,
                ExecutionTimeMs = executionTime,
                StartTime = startTime,
                EndTime = endTime,
                SolutionId = this.SolutionId,
                ProjectId = this.ProjectId,
                Creator = this.Context.clientID
            };

            await db.InsertAsync(log);
        }

        private async Task UpdateExecutionStats(string actionLibraryId, int executionTime)
        {
            using var db = this.GetDb();

            var actionLibrary = await db.LcActionLibraries
                .Where(a => a.Id == actionLibraryId)
                .FirstOrDefaultAsync();

            if (actionLibrary != null)
            {
                actionLibrary.ExecutionCount++;
                actionLibrary.LastExecutionTime = DateTime.Now;

                // 计算平均执行时间
                if (actionLibrary.AverageExecutionTime.HasValue)
                {
                    actionLibrary.AverageExecutionTime =
                        (int)((actionLibrary.AverageExecutionTime.Value * (actionLibrary.ExecutionCount - 1) + executionTime)
                              / actionLibrary.ExecutionCount);
                }
                else
                {
                    actionLibrary.AverageExecutionTime = executionTime;
                }

                await db.UpdateAsync(actionLibrary);
            }
        }
    }
}
