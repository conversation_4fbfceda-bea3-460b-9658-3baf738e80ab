<template>
  <t-dialog
    v-model:visible="showValueDialog"
    attach="body"
    header="选择值"
    width="80%"
    height="80vh"
    top="10vh"
    :footer="false"
  >
    <div v-if="data" class="value-container">
      <div v-if="!onlyVariable" class="header">
        <t-space>
          <div class="title">类型</div>
          <t-radio-group v-model:value="data.type" variant="primary-filled">
            <t-radio-button
              v-for="item in valueTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></t-radio-button>
          </t-radio-group>
          <!-- <t-select v-model:value="data.type" :options="valueTypeOptions" placeholder="请选择类型"></t-select> -->
          <t-input v-if="data.type === 'script'" v-model="data.scriptName" placeholder="脚本名称"></t-input>
          <t-button v-if="data.type === 'script'" theme="default" variant="outline" @click="onClickScriptTest"
            >测试</t-button
          >
        </t-space>
        <div v-if="data.type !== 'variable'" class="toolbar-right">
          <t-button @click="onClickSave">保存</t-button>
        </div>
      </div>
      <!-- only-variable 模式下的清空按钮 -->
      <div v-if="onlyVariable && data.variableValue" class="header">
        <t-space>
          <div v-if="data.variableValue" class="selected-variable">
            已选择: {{ data.variableName || data.variableValue }}
          </div>
        </t-space>
        <div class="toolbar-right">
          <t-button v-if="data.variableValue" theme="default" variant="outline" @click="onClickClear"
            >清空选择</t-button
          >
        </div>
      </div>
      <div v-if="data.type === 'text' || data.type === 'script' || data.type === 'visual'" class="content">
        <t-textarea
          v-show="data.type === 'text'"
          v-model="data.textValue"
          placeholder="请输入文本"
          :autosize="{ minRows: 5, maxRows: 12 }"
        ></t-textarea>
        <editor
          v-show="data.type === 'script'"
          ref="editorRef"
          v-model:value="data.scriptValue"
          :language="codeLanguage"
          :enable-intellisense="true"
          :current-variables="actionFlowStore.currentVariables"
          :local-variables="actionFlowStore.localVariables"
          :global-variables="globalVariables"
          :functions="functionList"
          style="height: 250px"
        ></editor>
        <div v-show="data.type === 'visual'" class="visual-function-container">
          <visual-function-composer
            :key="`visual-composer-${currentValueInputData?.id || 'default'}`"
            v-model="data.visualSteps"
            :input-data="sampleInputData"
            @test="onTestVisualFunction"
            @change="onVisualFunctionChange"
            style="height: 650px"
          />
        </div>
      </div>
      <div v-if="data.type == 'script' || data.type == 'variable'" class="footer">
        <t-tabs v-model="activeTab" @change="onChangeTab">
          <t-tab-panel :value="0" label="临时变量">
            <variable-tree
              ref="currentVariableTreeRef"
              v-model:active-id="data.variableValue"
              :filter-text="searchText"
              :variable-list="actionFlowStore.currentVariables"
              :limit-types="limitTypes"
              @dblclick="onDblclickVariable"
            ></variable-tree>
          </t-tab-panel>
          <t-tab-panel :value="1" label="局部变量">
            <variable-tree
              ref="localVariableTreeRef"
              v-model:active-id="data.variableValue"
              :filter-text="searchText"
              :variable-list="actionFlowStore.localVariables"
              :limit-types="limitTypes"
              @dblclick="onDblclickVariable"
            ></variable-tree>
          </t-tab-panel>
          <t-tab-panel :value="2" label="全局变量">
            <variable-tree
              v-model:active-id="data.variableValue"
              :filter-text="searchText"
              :variable-list="globalVariables"
              :limit-types="limitTypes"
              @dblclick="onDblclickVariable"
            ></variable-tree>
          </t-tab-panel>
          <t-tab-panel v-if="data.type === 'script'" :value="3" label="函数">
            <enhanced-function-list @dblclick="onDblclickFunction"></enhanced-function-list>
          </t-tab-panel>
          <template #action>
            <t-input v-model="searchText" class="search-input" size="small" placeholder="搜索" clearable>
              <template #prefixIcon>
                <search-icon></search-icon>
              </template>
            </t-input>
          </template>
        </t-tabs>
      </div>
    </div>
  </t-dialog>

  <!-- 脚本测试对话框 -->
  <t-dialog
    v-model:visible="scriptTestVisible"
    attach="body"
    header="脚本测试"
    width="70%"
    height="70vh"
    top="15vh"
    :footer="false"
  >
    <div class="script-test-container">
      <div class="test-input-section">
        <div class="section-title">输入参数</div>
        <div class="input-editor">
          <editor
            v-model:value="scriptTestInputDataJson"
            language="json"
            style="height: 200px"
            placeholder="请编辑测试输入数据（JSON格式）"
          ></editor>
        </div>
      </div>

      <div class="test-actions">
        <t-button theme="primary" @click="executeScriptTest" :loading="scriptTestLoading"> 执行测试 </t-button>
        <t-button theme="default" @click="scriptTestVisible = false"> 关闭 </t-button>
      </div>

      <div v-if="scriptTestResult" class="test-result-section">
        <div class="section-title">测试结果</div>
        <div class="result-content">
          <div v-if="scriptTestResult.success !== false" class="success-result">
            <div class="result-label">执行成功</div>
            <code-preview :code="scriptTestResult.result.toString()" lang="json"></code-preview>
            <div v-if="scriptTestResult.executionTimeMs" class="execution-time">
              执行时间: {{ scriptTestResult.executionTimeMs }}ms
            </div>
          </div>
          <div v-else class="error-result">
            <div class="result-label">执行失败</div>
            <div class="error-message">{{ scriptTestResult.errorMessage }}</div>
            <div v-if="scriptTestResult.executionTimeMs" class="execution-time">
              执行时间: {{ scriptTestResult.executionTimeMs }}ms
            </div>
          </div>
        </div>
      </div>
    </div>
  </t-dialog>
</template>
<script lang="ts">
export default {
  name: 'ValueDialog',
};
</script>
<script setup lang="ts">
import { isEmpty } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { SearchIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';
import { computed, nextTick, onActivated, onMounted, ref, watch } from 'vue';
import dayjs from 'dayjs';

import { useActionFlowStore } from '@/components/action-panel/store/index';
import Editor from '@/components/editor/index.vue';
import { getAllFunctions, type FunctionItem } from '@/composables/services/functionDataService';

import EnhancedFunctionList from './EnhancedFunctionList.vue';
import { FlowData, FlowDataValue } from './model';
import { getGlobalVariables } from './utils';
import VariableTree from './VariableTree.vue';
import VisualFunctionComposer from './VisualFunctionComposer.vue';
import { api, Services } from '@/api/system';
import CodePreview from '@/components/code-preview/index.vue';

const actionFlowStore = useActionFlowStore();

const { currentValueInputData, showValueDialog } = storeToRefs(actionFlowStore);

// 转换后端数据为前端格式
const convertVisualDataFromBackend = (dataValue: any) => {
  if (!dataValue) return dataValue;

  // 深拷贝数据，避免引用问题
  const clonedData = JSON.parse(JSON.stringify(dataValue));

  if (clonedData?.type === 'visual') {
    // 处理 visual 类型的数据
    if (clonedData.visualSteps) {
      // 如果visualSteps是字符串，需要解析
      if (typeof clonedData.visualSteps === 'string') {
        try {
          const steps = JSON.parse(clonedData.visualSteps);
          // 确保steps是数组
          if (Array.isArray(steps)) {
            // 转换回前端格式
            const convertedSteps = steps.map((step: any) => ({
              id: step.id,
              functionName: step.functionName,
              functionType: step.functionType === 0 ? 'builtin' : step.functionType === 1 ? 'csharp' : 'javascript',
              displayName: step.displayName,
              description: step.description,
              parameters:
                step.parameters?.map((param: any) => ({
                  name: param.name,
                  type: param.type === 0 ? 'text' : param.type === 1 ? 'variable' : 'previousResult',
                  value: param.value,
                  required: param.required,
                })) || [],
              outputVariable: step.outputVariable,
              order: step.order,
            }));
            clonedData.visualSteps = convertedSteps;
          } else {
            console.warn('visualSteps解析后不是数组:', steps);
            clonedData.visualSteps = [];
          }
        } catch (error) {
          console.error('解析visual步骤失败:', error);
          clonedData.visualSteps = [];
        }
      } else if (Array.isArray(clonedData.visualSteps)) {
        // 如果已经是数组，确保数据格式正确
        clonedData.visualSteps = clonedData.visualSteps.map((step: any) => ({
          id: step.id || `step_${Date.now()}_${Math.random()}`,
          functionName: step.functionName || '',
          functionType: step.functionType || 'builtin',
          displayName: step.displayName || step.functionName || '',
          description: step.description || '',
          parameters:
            step.parameters?.map((param: any) => ({
              name: param.name || '',
              type: param.type || 'text',
              value: param.value || '',
              required: param.required || false,
            })) || [],
          outputVariable: step.outputVariable || '',
          order: step.order || 0,
        }));
      } else {
        // 其他情况重置为空数组
        clonedData.visualSteps = [];
      }
    } else {
      // 如果没有 visualSteps，初始化为空数组
      clonedData.visualSteps = [];
    }
  } else {
    // 如果不是 visual 类型，确保 visualSteps 为空数组（避免残留数据）
    clonedData.visualSteps = [];
  }

  return clonedData;
};

const globalVariables = ref<FlowData[]>([]);

// 函数列表数据，用于智能提示
const functionList = ref<FunctionItem[]>([]);

// 加载函数数据
const loadFunctions = async () => {
  try {
    const functions = await getAllFunctions();
    functionList.value = functions;
    console.log(`ValueDialog: 已加载 ${functions.length} 个函数`);
  } catch (error) {
    console.error('ValueDialog: 加载函数数据失败:', error);
    functionList.value = [];
  }
};

// 初始化数据
const initializeData = async () => {
  globalVariables.value = await getGlobalVariables();
  await loadFunctions();
};

onActivated(initializeData);
onMounted(initializeData);

const data = ref<FlowDataValue>(convertVisualDataFromBackend(currentValueInputData.value?.value));
const onlyVariable = computed(() => currentValueInputData.value?.onlyVariable);
const limitTypes = computed(() => currentValueInputData.value?.limitTypes);

// const emits = defineEmits(['variable-change']);

const onClickSave = () => {
  console.log('ValueDialog onClickSave - before conversion:', {
    type: data.value?.type,
    visualStepsType: typeof data.value?.visualSteps,
    visualStepsLength: Array.isArray(data.value?.visualSteps) ? data.value?.visualSteps.length : 'not array',
  });

  // 对于visual类型，需要转换数据格式
  const valueToSave = convertVisualDataForBackend(data.value);

  console.log('ValueDialog onClickSave - after conversion:', {
    type: valueToSave?.type,
    visualStepsType: typeof valueToSave?.visualSteps,
    visualStepsIsString: typeof valueToSave?.visualSteps === 'string',
    visualStepsContent:
      typeof valueToSave?.visualSteps === 'string' ? valueToSave.visualSteps.substring(0, 100) + '...' : 'not string',
  });

  actionFlowStore.currentValueInputData.value = valueToSave;
  actionFlowStore.showValueDialog = false;
  actionFlowStore.isSaveValue = true;
};

const onClickClear = () => {
  // 清空变量选择
  data.value.variableType = '';
  data.value.variableName = '';
  data.value.variableValue = '';
  data.value.dataType = '';

  // 保存清空后的数据
  onClickSave();
};

// 脚本测试相关状态
const scriptTestVisible = ref(false);
const scriptTestLoading = ref(false);
const scriptTestResult = ref<any>(null);
const scriptTestInputData = ref<any>({});

// 脚本测试输入数据的JSON字符串表示
const scriptTestInputDataJson = computed({
  get: () => JSON.stringify(scriptTestInputData.value, null, 2),
  set: (value: string) => {
    try {
      scriptTestInputData.value = JSON.parse(value);
    } catch (error) {
      console.warn('Invalid JSON input:', error);
    }
  },
});

// 脚本测试方法
const onClickScriptTest = () => {
  if (!data.value.scriptValue?.trim()) {
    MessagePlugin.warning('请先输入脚本内容');
    return;
  }

  // 自动提取脚本中的变量作为入参
  extractScriptParameters();
  scriptTestVisible.value = true;
};

// 提取脚本参数
const extractScriptParameters = () => {
  const script = data.value.scriptValue || '';
  const inputData = {};

  // 分析脚本中使用的变量
  const usedVariables = analyzeScriptVariables(script);

  // 根据分析结果构建输入数据
  usedVariables.forEach((varPath: string) => {
    // 查找对应的变量定义
    const variable = findVariableByPath(varPath);
    console.log('处理变量:', { varPath, variable });
    if (variable) {
      const sampleValue = getSampleValue(variable.type);
      console.log('生成的示例值:', { varPath, type: variable.type, sampleValue });
      setNestedValue(inputData, varPath, sampleValue);
    } else {
      console.log('未找到变量定义:', varPath);
    }
  });

  scriptTestInputData.value = inputData;
};

// 分析脚本中使用的变量
const analyzeScriptVariables = (script: string) => {
  const usedVariables = new Set();

  console.log('分析脚本:', script);
  console.log(
    '可用的局部变量:',
    actionFlowStore.localVariables.map((v) => v.path),
  );
  console.log(
    '可用的临时变量:',
    actionFlowStore.currentVariables.map((v) => v.path),
  );

  // 移除注释，但保留字符串中的变量引用
  let cleanScript = script
    .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
    .replace(/\/\/.*$/gm, ''); // 移除单行注释

  // 先处理模板字符串中的变量引用
  const templateStringPattern = /`([^`]*)`/g;
  let templateMatch: RegExpExecArray | null;
  while ((templateMatch = templateStringPattern.exec(script)) !== null) {
    const templateContent = templateMatch[1];
    console.log('模板字符串内容:', templateContent);
    // 在模板字符串中查找 ${} 表达式
    const expressionPattern = /\$\{([^}]+)\}/g;
    let exprMatch: RegExpExecArray | null;
    while ((exprMatch = expressionPattern.exec(templateContent)) !== null) {
      const expression = exprMatch[1];
      console.log('模板表达式:', expression);
      // 递归分析表达式中的变量
      const exprVariables = analyzeExpression(expression);
      exprVariables.forEach((v: string) => usedVariables.add(v));
    }
  }

  // 移除字符串字面量
  cleanScript = cleanScript
    .replace(/"[^"]*"/g, '""') // 移除双引号字符串
    .replace(/'[^']*'/g, "''") // 移除单引号字符串
    .replace(/`[^`]*`/g, '``'); // 移除模板字符串

  console.log('清理后的脚本:', cleanScript);

  // 匹配局部变量（通过_data访问）- 优先处理，因为更具体
  const localVarPattern = /_data\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/g;
  let match: RegExpExecArray | null;

  while ((match = localVarPattern.exec(cleanScript)) !== null) {
    const varPath = match[1];
    console.log('找到局部变量路径:', varPath);

    // 检查是否是局部变量或其子路径
    const matchingVariable = actionFlowStore.localVariables.find(
      (v) => v.path && (v.path === varPath || varPath.startsWith(v.path + '.') || v.path.startsWith(varPath + '.')),
    );

    if (matchingVariable) {
      usedVariables.add('_data.' + varPath);
      console.log('添加局部变量:', '_data.' + varPath);
    } else {
      console.log('未找到匹配的局部变量定义:', varPath);
    }
  }

  // 匹配临时变量（直接访问）
  const currentVarPattern = /\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b/g;

  while ((match = currentVarPattern.exec(cleanScript)) !== null) {
    const varPath = match[1];

    // 排除JavaScript关键字和内置对象
    if (!isJavaScriptKeyword(varPath) && !isBuiltinObject(varPath)) {
      console.log('检查临时变量:', varPath);

      // 检查是否是临时变量或其子路径
      const matchingVariable = actionFlowStore.currentVariables.find(
        (v) => v.path && (v.path === varPath || varPath.startsWith(v.path + '.') || v.path.startsWith(varPath + '.')),
      );

      if (matchingVariable) {
        usedVariables.add(varPath);
        console.log('添加临时变量:', varPath);
      }
    }
  }

  console.log('最终提取的变量:', Array.from(usedVariables));
  return Array.from(usedVariables);
};

// 分析表达式中的变量
const analyzeExpression = (expression: string): string[] => {
  const variables: string[] = [];

  // 匹配局部变量（通过_data访问）
  const localVarPattern = /_data\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/g;
  let match: RegExpExecArray | null;

  while ((match = localVarPattern.exec(expression)) !== null) {
    const varPath = match[1];
    const matchingVariable = actionFlowStore.localVariables.find(
      (v) => v.path && (v.path === varPath || varPath.startsWith(v.path + '.') || v.path.startsWith(varPath + '.')),
    );

    if (matchingVariable) {
      variables.push('_data.' + varPath);
    }
  }

  // 匹配临时变量（直接访问）
  const currentVarPattern = /\b([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\b/g;

  while ((match = currentVarPattern.exec(expression)) !== null) {
    const varPath = match[1];

    if (!isJavaScriptKeyword(varPath) && !isBuiltinObject(varPath)) {
      const matchingVariable = actionFlowStore.currentVariables.find(
        (v) => v.path && (v.path === varPath || varPath.startsWith(v.path + '.') || v.path.startsWith(varPath + '.')),
      );

      if (matchingVariable) {
        variables.push(varPath);
      }
    }
  }

  return variables;
};

// 检查是否是JavaScript关键字
const isJavaScriptKeyword = (word: string) => {
  const keywords = [
    'break',
    'case',
    'catch',
    'class',
    'const',
    'continue',
    'debugger',
    'default',
    'delete',
    'do',
    'else',
    'export',
    'extends',
    'finally',
    'for',
    'function',
    'if',
    'import',
    'in',
    'instanceof',
    'let',
    'new',
    'return',
    'super',
    'switch',
    'this',
    'throw',
    'try',
    'typeof',
    'var',
    'void',
    'while',
    'with',
    'yield',
    'true',
    'false',
    'null',
    'undefined',
  ];
  return keywords.includes(word.split('.')[0]);
};

// 检查是否是内置对象
const isBuiltinObject = (word: string) => {
  const builtins = [
    'console',
    'Math',
    'Date',
    'Array',
    'Object',
    'String',
    'Number',
    'Boolean',
    'RegExp',
    'JSON',
    'parseInt',
    'parseFloat',
    'isNaN',
    'isFinite',
    'Utils',
    'window',
    'document',
  ];
  return builtins.includes(word.split('.')[0]);
};

// 根据路径查找变量定义
const findVariableByPath = (varPath: string) => {
  console.log('查找变量路径:', varPath);

  // 处理_data前缀的局部变量
  if (varPath.startsWith('_data.')) {
    const localPath = varPath.substring(6); // 移除'_data.'前缀
    return findVariableInList(actionFlowStore.localVariables, localPath);
  }

  // 查找临时变量
  return findVariableInList(actionFlowStore.currentVariables, varPath);
};

// 在变量列表中查找具体路径的变量
const findVariableInList = (variables: any[], targetPath: string) => {
  console.log('在变量列表中查找:', { targetPath, variableCount: variables.length });

  // 首先尝试精确匹配
  let exactMatch = variables.find((v) => v.path === targetPath);
  if (exactMatch) {
    console.log('找到精确匹配:', exactMatch);
    return exactMatch;
  }

  // 如果没有精确匹配，尝试在嵌套结构中查找
  for (const variable of variables) {
    if (variable.path && targetPath.startsWith(variable.path + '.')) {
      // 这是一个嵌套路径，需要在children中查找
      const remainingPath = targetPath.substring(variable.path.length + 1);
      const nestedVariable = findNestedVariable(variable, remainingPath);
      if (nestedVariable) {
        console.log('找到嵌套变量:', nestedVariable);
        return nestedVariable;
      }
    }
  }

  // 如果还是没找到，返回最接近的父级变量
  const parentMatch = variables.find((v) => v.path && targetPath.startsWith(v.path + '.'));
  console.log('返回父级匹配:', parentMatch);
  return parentMatch;
};

// 在变量的children中递归查找
const findNestedVariable = (parentVariable: any, remainingPath: string): any => {
  if (!parentVariable.children || !Array.isArray(parentVariable.children)) {
    return null;
  }

  const pathParts = remainingPath.split('.');
  const currentKey = pathParts[0];
  const restPath = pathParts.slice(1).join('.');

  for (const child of parentVariable.children) {
    if (child.key === currentKey) {
      if (restPath) {
        // 还有更深的路径，继续递归
        return findNestedVariable(child, restPath);
      } else {
        // 找到了目标字段
        return child;
      }
    }
  }

  return null;
};

// 在对象中设置嵌套值
const setNestedValue = (obj: any, path: string, value: any) => {
  console.log('设置嵌套值:', { path, value });
  const keys = path.split('.');
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!current[key]) {
      current[key] = {};
    }
    current = current[key];
  }

  const finalKey = keys[keys.length - 1];
  current[finalKey] = value;
  console.log('设置完成:', { finalKey, value, currentObject: current });
};

// 获取示例值
const getSampleValue = (type: string) => {
  console.log('获取示例值，类型:', type);
  type = type.toLowerCase();
  switch (type) {
    case 'string':
    case 'text': // 添加对text类型的支持
      return 'sample text';
    case 'number':
    case 'int':
    case 'integer':
    case 'decimal':
    case 'float':
    case 'double':
      return 123;
    case 'boolean':
    case 'bool':
      return true;
    case 'array':
    case 'list':
      return [1, 2, 3];
    case 'object':
    case 'json':
      return { key: 'value' };
    case 'date':
      return dayjs().format('YYYY-MM-DD');
    case 'datetime':
    case 'timestamp':
      return dayjs().format('YYYY-MM-DD HH:mm:ss');
    case 'time':
      return dayjs().format('HH:mm:ss');
    default:
      // 对于未知类型，默认返回字符串
      console.log('未知类型，默认返回字符串:', type);
      return 'sample text';
  }
};
// // 提取脚本参数
// const extractScriptParameters = () => {
//   const script = data.value.scriptValue || '';
//   const inputData: any = {};

//   // 从当前变量中构建示例数据
//   actionFlowStore.currentVariables.forEach((variable) => {
//     if (variable.path) {
//       const keys = variable.path.split('.');
//       let current = inputData;
//       for (let i = 0; i < keys.length - 1; i++) {
//         if (!current[keys[i]]) {
//           current[keys[i]] = {};
//         }
//         current = current[keys[i]];
//       }
//       current[keys[keys.length - 1]] =
//         variable.type === 'string'
//           ? 'sample text'
//           : variable.type === 'number'
//             ? 123
//             : variable.type === 'boolean'
//               ? true
//               : null;
//     }
//   });

//   // 添加_data对象用于局部变量访问
//   if (actionFlowStore.localVariables.length > 0) {
//     inputData._data = {};
//     actionFlowStore.localVariables.forEach((variable) => {
//       if (variable.path) {
//         const keys = variable.path.split('.');
//         let current = inputData._data;
//         for (let i = 0; i < keys.length - 1; i++) {
//           if (!current[keys[i]]) {
//             current[keys[i]] = {};
//           }
//           current = current[keys[i]];
//         }
//         current[keys[keys.length - 1]] =
//           variable.type === 'string'
//             ? 'sample text'
//             : variable.type === 'number'
//               ? 123
//               : variable.type === 'boolean'
//                 ? true
//                 : null;
//       }
//     });
//   }

//   scriptTestInputData.value = inputData;
// };

// 执行脚本测试
const executeScriptTest = async () => {
  if (!data.value.scriptValue?.trim()) {
    MessagePlugin.warning('请先输入脚本内容');
    return;
  }

  scriptTestLoading.value = true;
  try {
    const response = await api.run(Services.scriptTest, {
      script: data.value.scriptValue,
      inputData: JSON.stringify(scriptTestInputData.value),
    });

    scriptTestResult.value = response;

    if (response.success !== false) {
      MessagePlugin.success('脚本测试执行成功');
    } else {
      MessagePlugin.error(`脚本测试失败: ${response.errorMessage || '未知错误'}`);
    }
  } catch (error) {
    console.error('脚本测试失败:', error);
    scriptTestResult.value = {
      success: false,
      errorMessage: error.message || '测试执行失败',
    };
    MessagePlugin.error(`脚本测试失败: ${error.message}`);
  } finally {
    scriptTestLoading.value = false;
  }
};

const searchText = ref('');
const valueTypeOptions = computed(() => {
  return [
    { label: '纯文本', value: 'text' },
    { label: '变量', value: 'variable' },
    { label: '动态脚本', value: 'script' },
    { label: '可视化函数', value: 'visual' },
  ];
});

const codeLanguage = computed(() => {
  if (data.value.type === 'script') {
    return 'typescript';
  }
  return 'plaintext';
});

const localVariableTreeRef = ref();
const currentVariableTreeRef = ref();

const activeTab = ref(0);

const onChangeTab = (value: any) => {
  nextTick(() => {
    if (value === 0) {
      currentVariableTreeRef.value.scrollToActive();
    } else if (value === 1) {
      localVariableTreeRef.value.scrollToActive();
    }
  });
};

watch(showValueDialog, (val) => {
  if (val) {
    setActiveTab();
    // 重置脚本测试相关状态
    scriptTestResult.value = null;
    scriptTestVisible.value = false;
    scriptTestLoading.value = false;
    scriptTestInputData.value = {};
  }
});

watch(
  () => data.value?.type,
  (val) => {
    if (val === 'variable' && activeTab.value >= 2) {
      activeTab.value = 0;
    }
  },
);

const setActiveTab = () => {
  const valData = currentValueInputData.value?.value;
  if (!valData || valData.type !== 'variable') return;
  if (valData.type === 'variable') {
    if (valData.variableType === 'current') {
      activeTab.value = 0;
    } else if (valData.variableType === 'local') {
      activeTab.value = 1;
    } else {
      activeTab.value = 2;
    }
    onChangeTab(activeTab.value);
  }
};

watch(
  currentValueInputData,
  (newValue) => {
    console.log('ValueDialog watch currentValueInputData:', {
      newValue: newValue?.value,
      type: newValue?.value?.type,
      visualStepsType: typeof newValue?.value?.visualSteps,
      visualStepsLength: Array.isArray(newValue?.value?.visualSteps)
        ? newValue?.value?.visualSteps.length
        : 'not array',
    });

    if (newValue?.value) {
      // 确保对visual类型的数据进行正确的转换
      const convertedData = convertVisualDataFromBackend(newValue.value);
      console.log('ValueDialog converted data:', {
        type: convertedData?.type,
        visualStepsType: typeof convertedData?.visualSteps,
        visualStepsLength: Array.isArray(convertedData?.visualSteps) ? convertedData?.visualSteps.length : 'not array',
      });
      data.value = convertedData;
    } else {
      data.value = newValue?.value;
    }
  },
  {
    deep: true,
  },
);

watch(
  () => currentValueInputData.value?.value?.type,
  (val) => {
    if (val === 'variable') {
      setActiveTab();
    } else if (val === 'script') {
      activeTab.value = 3;
    }
  },
);

const editorRef = ref();
const onDblclickFunction = (func: any) => {
  editorRef.value.insertText(func.script);
  if (isEmpty(data.value.scriptName)) data.value.scriptName = func.label;
};

// 可视化函数相关
const sampleInputData = computed(() => {
  // 从当前变量中构建示例数据
  const inputData: any = {};
  actionFlowStore.currentVariables.forEach((variable) => {
    if (variable.path) {
      const keys = variable.path.split('.');
      let current = inputData;
      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }
      current[keys[keys.length - 1]] =
        variable.type === 'string'
          ? 'sample text'
          : variable.type === 'number'
            ? 123
            : variable.type === 'boolean'
              ? true
              : null;
    }
  });
  return inputData;
});

// 转换前端数据结构为后端期望的格式
const convertStepsForBackend = (steps: any[]) => {
  return steps.map((step) => ({
    id: step.id,
    functionName: step.functionName,
    functionType: step.functionType === 'builtin' ? 0 : step.functionType === 'csharp' ? 1 : 2,
    displayName: step.displayName,
    description: step.description,
    parameters: step.parameters.map((param: any) => ({
      name: param.name,
      type: param.type === 'text' ? 0 : param.type === 'variable' ? 1 : 2,
      value: param.value,
      required: param.required,
    })),
    outputVariable: step.outputVariable,
    order: step.order,
  }));
};

const onTestVisualFunction = async (steps: any[]) => {
  try {
    // 转换数据格式
    const convertedSteps = convertStepsForBackend(steps);

    // 调用后端API进行测试
    const response = await api.run(Services.visualFunctionExecute, {
      steps: convertedSteps,
      inputData: sampleInputData.value,
      generateExpressionOnly: false,
    });

    console.log('可视化函数测试结果:', response);

    // 可以在这里显示测试结果的弹窗或通知
    if (response.success !== false) {
      console.log('执行成功，结果:', response.result);
    } else {
      console.error('执行失败:', response.errorMessage);
    }
  } catch (error) {
    console.error('测试失败:', error);
  }
};

const onVisualFunctionChange = (steps: any[]) => {
  console.log('ValueDialog onVisualFunctionChange:', {
    stepsLength: steps?.length,
    stepsType: typeof steps,
    isArray: Array.isArray(steps),
  });

  if (data.value) {
    data.value.visualSteps = steps;
    // 生成可视化函数的名称
    if (steps.length > 0) {
      data.value.visualName = `可视化函数(${steps.length}步)`;
    } else {
      data.value.visualName = '';
    }
  }
};

// 转换visual数据为后端格式
const convertVisualDataForBackend = (dataValue: any) => {
  if (!dataValue) return dataValue;

  // 深拷贝数据，避免修改原始数据
  const clonedData = JSON.parse(JSON.stringify(dataValue));

  if (clonedData.type === 'visual' && clonedData.visualSteps) {
    // 如果visualSteps已经是字符串，直接返回
    if (typeof clonedData.visualSteps === 'string') {
      return clonedData;
    }

    // 如果是数组，转换为后端格式并序列化
    if (Array.isArray(clonedData.visualSteps)) {
      try {
        const convertedSteps = convertStepsForBackend(clonedData.visualSteps);
        clonedData.visualSteps = JSON.stringify(convertedSteps);
      } catch (error) {
        console.error('转换visual步骤失败:', error);
        clonedData.visualSteps = '[]';
      }
    } else {
      // 其他情况重置为空数组字符串
      clonedData.visualSteps = '[]';
    }
  }

  return clonedData;
};

const onDblclickVariable = ({ path, item }) => {
  if (data.value.type === 'script') {
    const prefix = activeTab.value === 0 ? '' : '_data.';
    editorRef.value.insertText(prefix + path);
    return;
  }
  if (data.value.type === 'variable') {
    if (activeTab.value === 0) {
      data.value.variableType = 'current';
    } else if (activeTab.value === 1) {
      data.value.variableType = 'local';
    } else {
      data.value.variableType = 'global';
    }

    const itemData: FlowData = item.data;
    data.value.dataType = itemData.type;
    data.value.variableValue = path;
    data.value.variableName = itemData.pathDescription || itemData.description;
    // emits('variable-change', { path, item: itemData });
    onClickSave();
  }
};
</script>
<style lang="less" scoped>
.search-input {
  position: relative;
  top: 8px;
  width: 180px;
}

.value-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  > .header {
    padding: 8px 0;
    border-bottom: 1px solid var(--td-component-stroke);
    flex-shrink: 0; /* 防止头部被压缩 */

    .toolbar-right {
      float: right;
    }
  }

  > .content {
    flex: 1;
    padding: 8px 0;
  }

  > .footer {
    height: 450px;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    /* 确保 tabs 组件占用全部可用高度 */
    :deep(.t-tabs) {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    /* tabs 头部固定，内容区域自适应 */
    :deep(.t-tabs__header) {
      flex-shrink: 0;
    }

    :deep(.t-tabs__content) {
      flex: 1;
      overflow: hidden;
    }

    /* tab 面板占用全部高度 */
    :deep(.t-tab-panel) {
      height: 100%;
      overflow: hidden;
    }

    /* 确保 variable-tree 组件占用全部高度 */
    :deep(.variable-tree) {
      height: 100%;
      padding-top: 8px;
    }

    /* 修复双重滚动条问题 - 确保只有内层树有滚动条 */
    :deep(.variable-tree .tree-container) {
      overflow: hidden;
    }

    :deep(.variable-tree .tree-container .t-tree) {
      height: 100%;
      overflow-y: auto;
      overflow-x: hidden;
      max-height: none !important;
    }

    /* 移除 TDesign 树组件的默认高度限制 */
    :deep(.variable-tree .t-tree__list) {
      max-height: none !important;
      height: auto !important;
    }

    :deep(.variable-tree .t-tree__scroll) {
      height: 100% !important;
      max-height: none !important;
    }

    /* 确保 tabs 内容区域不产生滚动条 */
    :deep(.t-tabs__content) {
      overflow: hidden !important;
    }

    :deep(.t-tab-panel) {
      overflow: hidden !important;
    }

    /* 确保变量树占用全部可用空间 */
    :deep(.variable-tree) {
      height: 100% !important;
      max-height: none !important;
    }

    /* 强制移除可能的高度限制 */
    :deep(.t-tree) {
      max-height: none !important;
    }

    :deep(.t-tree__inner) {
      max-height: none !important;
    }

    /* 确保只有在内容真正超出时才显示滚动条 */
    :deep(.variable-tree .tree-container .t-tree) {
      overflow-y: auto !important;
      overflow-x: hidden !important;
    }

    /* 确保 EnhancedFunctionList 组件占用全部高度 */
    :deep(.enhanced-function-list) {
      height: 100% !important;
      max-height: none !important;
    }

    /* 确保函数列表容器占用全部高度 */
    :deep(.enhanced-function-list .function-list-container) {
      height: 100% !important;
      max-height: none !important;
    }

    /* 确保函数详情面板可以滚动 */
    :deep(.enhanced-function-list .function-detail-panel) {
      overflow-y: auto !important;
      overflow-x: hidden !important;
    }

    /* 确保函数详情内容可以正常显示 */
    :deep(.enhanced-function-list .function-detail) {
      overflow-y: auto !important;
      overflow-x: hidden !important;
    }
  }
}

.selected-variable {
  color: var(--td-text-color-secondary);
  font-size: 12px;
  padding: 4px 8px;
  background-color: var(--td-bg-color-container-select);
  border-radius: 3px;
}

.visual-function-container {
  min-height: 400px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  overflow: hidden;
}

.script-test-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 16px;
}

.test-input-section {
  flex: 1;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--td-text-color-primary);
  margin-bottom: 8px;
}

.input-editor {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  overflow: hidden;
}

.test-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding: 8px 0;
  border-top: 1px solid var(--td-border-level-1-color);
  border-bottom: 1px solid var(--td-border-level-1-color);
}

.test-result-section {
  flex: 1;
  min-height: 200px;
}

.result-content {
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  padding: 12px;
  background-color: var(--td-bg-color-container);
}

.success-result {
  .result-label {
    color: var(--td-success-color);
    font-weight: 500;
    margin-bottom: 8px;
  }
}

.error-result {
  .result-label {
    color: var(--td-error-color);
    font-weight: 500;
    margin-bottom: 8px;
  }

  .error-message {
    background-color: var(--td-error-color-1);
    border: 1px solid var(--td-error-color-3);
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    color: var(--td-error-color);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.execution-time {
  font-size: 12px;
  color: var(--td-text-color-secondary);
}
</style>
